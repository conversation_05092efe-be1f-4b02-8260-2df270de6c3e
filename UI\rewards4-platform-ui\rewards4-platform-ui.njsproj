<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003" ToolsVersion="4.0">
  <PropertyGroup>
    <VisualStudioVersion Condition="'$(VisualStudioVersion)' == ''">14.0</VisualStudioVersion>
    <VSToolsPath Condition="'$(VSToolsPath)' == ''">$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(VisualStudioVersion)</VSToolsPath>
    <Name>rewards4-platform-ui</Name>
    <RootNamespace>rewards4-platform-ui-vue</RootNamespace>
    <SaveNodeJsSettingsInProjectFile>True</SaveNodeJsSettingsInProjectFile>
    <ScriptArguments>serve</ScriptArguments>
  </PropertyGroup>
  <Target Name="Build">
    <Exec Command="npm install" />
    <Exec Command="npm run build" />
  </Target>
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{807d04ab-197e-494c-b51c-0c489436a847}</ProjectGuid>
    <ProjectHome>
    </ProjectHome>
    <StartupFile>node_modules\@vue\cli-service\bin\vue-cli-service.js</StartupFile>
    <StartWebBrowser>True</StartWebBrowser>
    <SearchPath>
    </SearchPath>
    <WorkingDirectory>.</WorkingDirectory>
    <OutputPath>.</OutputPath>
    <TargetFrameworkVersion>v4.0</TargetFrameworkVersion>
    <ProjectTypeGuids>{3AF33F2E-1136-4D97-BBB7-1795711AC8B8};{349c5851-65df-11da-9384-00065b846f21};{9092AA53-FB77-4645-B42D-1CCCA6BD08BD}</ProjectTypeGuids>
    <NodejsPort>1337</NodejsPort>
    <EnableTypeScript>true</EnableTypeScript>
    <StartWebBrowser>True</StartWebBrowser>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)' == 'Debug' ">
    <DebugSymbols>true</DebugSymbols>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)' == 'Release' ">
    <DebugSymbols>true</DebugSymbols>
  </PropertyGroup>
  <ItemGroup>
    <None Include="server.ts" />
    <Content Include=".gitignore" />
    <Content Include="public\favicon.ico" />
    <Content Include="src\App.vue" />
    <Content Include="src\assets\base.css" />
    <Content Include="src\assets\logo.svg" />
    <Content Include="src\assets\main.css" />
    <Content Include="src\components\homeView.vue" />
    <Content Include="src\components\SigninView.vue" />
    <Content Include="src\components\SignupView.vue" />
    <Content Include="tsconfig.json" />
    <Content Include="package.json" />
    <Content Include="README.md" />
  </ItemGroup>
  <ItemGroup>
    <Folder Include="src\" />
    <Folder Include="public\" />
    <Folder Include="src\assets\" />
    <Folder Include="src\components\" />
    <Folder Include="src\requests\" />
    <Folder Include="src\services\" />
  </ItemGroup>
  <ItemGroup>
    <TypeScriptCompile Include="src\main.ts" />
    <TypeScriptCompile Include="src\requests\SigninRequest.ts" />
    <TypeScriptCompile Include="src\requests\SingupRequest.ts" />
    <TypeScriptCompile Include="src\router.ts" />
    <TypeScriptCompile Include="src\services\authentication-service.ts" />
    <TypeScriptCompile Include="src\services\axios.ts">
      <SubType>Code</SubType>
    </TypeScriptCompile>
    <TypeScriptCompile Include="src\shims-vue.d.ts" />
  </ItemGroup>
  <ProjectExtensions>
    <VisualStudio>
      <FlavorProperties GUID="{349c5851-65df-11da-9384-00065b846f21}">
        <WebProjectProperties>
          <UseIIS>False</UseIIS>
          <AutoAssignPort>True</AutoAssignPort>
          <DevelopmentServerPort>0</DevelopmentServerPort>
          <DevelopmentServerVPath>/</DevelopmentServerVPath>
          <IISUrl>http://localhost:48022/</IISUrl>
          <NTLMAuthentication>False</NTLMAuthentication>
          <UseCustomServer>True</UseCustomServer>
          <CustomServerUrl>http://localhost:1337</CustomServerUrl>
          <SaveServerSettingsInUserFile>False</SaveServerSettingsInUserFile>
        </WebProjectProperties>
      </FlavorProperties>
      <FlavorProperties GUID="{349c5851-65df-11da-9384-00065b846f21}" User="">
        <WebProjectProperties>
          <StartPageUrl>
          </StartPageUrl>
          <StartAction>CurrentPage</StartAction>
          <AspNetDebugging>True</AspNetDebugging>
          <SilverlightDebugging>False</SilverlightDebugging>
          <NativeDebugging>False</NativeDebugging>
          <SQLDebugging>False</SQLDebugging>
          <ExternalProgram>
          </ExternalProgram>
          <StartExternalURL>
          </StartExternalURL>
          <StartCmdLineArguments>
          </StartCmdLineArguments>
          <StartWorkingDirectory>
          </StartWorkingDirectory>
          <EnableENC>False</EnableENC>
          <AlwaysStartWebServerOnDebug>False</AlwaysStartWebServerOnDebug>
        </WebProjectProperties>
      </FlavorProperties>
    </VisualStudio>
  </ProjectExtensions>
</Project>