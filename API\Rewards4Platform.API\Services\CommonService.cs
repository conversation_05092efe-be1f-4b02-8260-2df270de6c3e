﻿namespace Rewards4Platform.API.Services
{
    public interface ICommonService
    {
        CookieOptions SetCookieOptions();
    }

    public class CommonService : ICommonService
    {
        public CookieOptions SetCookieOptions()
        {
            var cookieOptions = new CookieOptions
            {
                HttpOnly = true,
                Secure = true,
                SameSite = SameSiteMode.None,
                Path = "/"
            };

            return cookieOptions;
        }
    }
}
