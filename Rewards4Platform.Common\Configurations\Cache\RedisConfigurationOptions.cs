﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Rewards4Platform.Common.Configurations.Cache
{
    public class RedisConfigurationOptions
    {
        public string ServerName { get; set; } = string.Empty;
        public int Port { get; set; }
        public string Password { get; set; } = string.Empty;
        public bool UseSsl { get; set; }
        public WebsiteConfig? Website { get; set; }
        public CmsConfig? Cms { get; set; }
    }

    public class WebsiteConfig
    {
        public string KeyPrefix { get; set; } = string.Empty;
        public int Database { get; set; }
    }

    public class CmsConfig
    {
        public string KeyPrefix { get; set; } = string.Empty;
        public int Database { get; set; }
    }
}
