{"name": "rewards4-platform-ui-vue", "version": "0.0.0", "description": "rewards4-platform-ui-vue", "main": "server.js", "author": {"name": ""}, "scripts": {"start": "ts-node src/server.ts", "serve": "vue-cli-service serve", "build": "vue-cli-service build"}, "dependencies": {"@vue/cli-plugin-babel": "~5.0.0", "axios": "^1.7.1", "core-js": "^3.8.3", "pinia": "^3.0.3", "vue": "^3.2.13", "vue-router": "^4.0.3"}, "devDependencies": {"@types/jest": "^27.0.1", "@types/node": "^14.14.7", "@typescript-eslint/eslint-plugin": "^5.33.0", "@typescript-eslint/parser": "^5.33.0", "@vue/cli-plugin-router": "~5.0.0", "@vue/cli-plugin-typescript": "~5.0.0", "@vue/cli-plugin-unit-jest": "~5.0.0", "@vue/cli-service": "~5.0.0", "@vue/test-utils": "^2.0.0-0", "@vue/vue3-jest": "^27.0.0-alpha.1", "babel-jest": "^27.0.6", "eslint": "^8.21.0", "jest": "^27.0.5", "sass": "^1.32.7", "sass-loader": "^12.0.0", "ts-jest": "^27.0.4", "typescript": "^4.5.2"}, "eslintConfig": {"parser": "@typescript-eslint/parser", "parserOptions": {"ecmaVersion": "latest", "sourceType": "module"}, "plugins": ["@typescript-eslint"]}}