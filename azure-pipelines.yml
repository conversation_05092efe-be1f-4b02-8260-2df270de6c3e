trigger:
- test

pool:
  vmImage: 'ubuntu-latest'

variables:
  nodeVersion: '20.x'
  buildConfiguration: 'Release'
  solution: '**/*.sln'
  webAppName: 'neu-r4g-app-rewards4platform-web' # Replace with your web app name
  apiAppName: 'neu-r4g-app-rewards4platform-api'
  azureSubscription: 'Microsoft Azure(10a18b90-c406-4a64-ae3d-ab7825161400)' # Replace with your Azure service connection
  projectPath: '**/*.csproj'

stages:
- stage: Build
  jobs:
  - job: Build_Node_Vue
    displayName: 'Build Node.js and Vue.js Application'
    steps:
    - task: NodeTool@0
      inputs:
        versionSpec: $(nodeVersion)
      displayName: 'Install Node.js'

    - script: |
        echo "Changing to the client directory"
        cd UI/rewards4-platform-ui
        echo "Current working directory:"
        pwd
        echo "Installing npm packages"
        npm install
        npm run build
      displayName: 'Build Vue.js Application'

    - script: |
        echo "Checking build output"
        cd UI/rewards4-platform-ui
        echo "Current working directory:"
        pwd
        echo "List of files in the client directory after build:"
        ls -la
        if [ -d "dist" ]; then
          echo "dist directory found"
          echo "List of files in the dist directory:"
          ls -la dist
        else
          echo "dist directory not found"
          fi
      displayName: 'Check build output'

    - task: ArchiveFiles@2
      inputs:
        rootFolderOrFile: 'UI/rewards4-platform-ui/dist'
        includeRootFolder: false
        archiveType: 'zip'
        archiveFile: '$(Build.ArtifactStagingDirectory)/vue.zip'
        replaceExistingArchive: true
      displayName: 'Archive Vue.js Build Artifacts'

    - script: |
        echo "Pipeline workspace path:"
        echo $(Pipeline.Workspace)
        echo "Contents of vue.zip:"
        unzip -l $(Pipeline.Workspace)/a/vue.zip
      displayName: 'Check Vue.js Build Artifacts'

    - task: PublishBuildArtifacts@1
      inputs:
        pathToPublish: '$(Build.ArtifactStagingDirectory)/vue.zip'
        artifactName: 'vue'
      displayName: 'Publish Vue.js Build Artifacts'

  - job: Build_DotNet
    displayName: 'Build .NET 8 Application'
    steps:
    - task: UseDotNet@2
      inputs:
        packageType: 'sdk'
        version: '8.x'
      displayName: 'Install .NET 8 SDK'

    - task: DotNetCoreCLI@2
      inputs:
        command: 'restore'
        projects: '$(solution)'
      displayName: 'Restore .NET Dependencies'

    - task: DotNetCoreCLI@2
      inputs:
        command: 'build'
        projects: '$(solution)'
        arguments: '--configuration $(buildConfiguration)'
      displayName: 'Build .NET Solution'

    - task: DotNetCoreCLI@2
      inputs:
        command: 'publish'
        arguments: '--configuration $(buildConfiguration) --output $(Build.ArtifactStagingDirectory) --no-restore'
        projects: '$(projectPath)'
      displayName: 'Publish .NET Solution'

    - script: |
        echo "Checking .NET publish output"
        echo "Current working directory:"
        pwd
        echo "List of files in the staging directory:"
        ls -la $(Build.ArtifactStagingDirectory)
      displayName: 'Check .NET publish output'
      
    - task: PublishBuildArtifacts@1
      inputs:
        pathToPublish: '$(Build.ArtifactStagingDirectory)'
        artifactName: 'dotnet'
        publishLocation: 'Container'
      displayName: 'Publish .NET Build Artifacts'

    - script: |
        echo "Pipeline workspace path:"
        echo $(Pipeline.Workspace)
        echo "List of files in the workspace directory:"
        ls -la $(Pipeline.Workspace)
        echo "List of files in the build artifact directory:"
        ls -la $(Build.ArtifactStagingDirectory)
      displayName: 'Echo Pipeline Workspace Path and Build Artifacts'

- stage: Deploy
  dependsOn: Build
  jobs:
  - deployment: Deploy
    displayName: 'Deploy to Azure App Service'
    environment: 'production'
    strategy:
      runOnce:
        deploy:
          steps:
          - download: current
            artifact: vue
            displayName: 'Download Vue.js Build Artifacts'

          - download: current
            artifact: dotnet
            displayName: 'Download .NET Build Artifacts'

          - task: AzureWebApp@1
            inputs:
              azureSubscription: $(azureSubscription)
              appType: 'webAppLinux'
              appName: $(webAppName)
              package: '$(Pipeline.Workspace)/vue/vue.zip'
              slotName: 'testing'
            displayName: 'Deploy Vue.js to Azure App Service'
          - script: |
              echo "Pipeline workspace path:"
              echo $(Pipeline.Workspace)/dotnet
              echo "List of files in the workspace directory:"
              ls -la $(Pipeline.Workspace)/dotnet
              echo "List of files in the build artifact directory:"
              ls -la $(Build.ArtifactStagingDirectory)
              echo "Contents of Rewards4Platform.API.zip:"
              unzip -l $(Pipeline.Workspace)/dotnet/Rewards4Platform.API.zip
            displayName: 'Echo Pipeline Workspace Path and Build Artifacts'

          - task: AzureWebApp@1
            inputs:
              azureSubscription: $(azureSubscription)
              appType: 'webAppLinux'
              appName: $(apiAppName)
              package: '$(Pipeline.Workspace)/dotnet/Rewards4Platform.API.zip'
              slotName: 'testing'
            displayName: 'Deploy .NET to Azure App Service'

