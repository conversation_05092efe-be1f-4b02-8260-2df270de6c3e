<template>
  <h1>Welcome To Rewards4 Platform. You are now logged in.</h1>
  <br />
  <button @click="logout">Logout</button>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'
import { useAuthStore } from '../stores/auth'

const router = useRouter()
const authStore = useAuthStore()

const logout = async () => {
  try {
    await authStore.signout()
    router.push('/')
  } catch (error) {
    console.error('Logout error:', error)
    router.push('/')
  }
}
</script>

<style>

</style>
