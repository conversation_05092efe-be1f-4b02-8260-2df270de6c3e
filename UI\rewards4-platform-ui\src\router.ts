import { createRouter, createWebHistory, RouteRecordRaw } from 'vue-router'
import SignupView from './components/SignupView.vue'

const routes: Array<RouteRecordRaw> = [
  {
    path: '/',
    name: 'signup',
    component: SignupView
  },
  {
    path: '/signin',
    name: 'signin',    
    component: () => import('./components/SigninView.vue')
  },
  {
    path: '/home',
    name: 'home',    
    component: () => import('./components/homeView.vue')
  }
]

const router = createRouter({
    history: createWebHistory(process.env.BASE_URL),
  routes
})

export default router
