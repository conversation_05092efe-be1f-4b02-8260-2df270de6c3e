C:\Work\Rewards4Platform\API\Rewards4Platform.API\bin\Debug\net8.0\appsettings.Development.json
C:\Work\Rewards4Platform\API\Rewards4Platform.API\bin\Debug\net8.0\Rewards4Platform.API.exe
C:\Work\Rewards4Platform\API\Rewards4Platform.API\bin\Debug\net8.0\Rewards4Platform.API.deps.json
C:\Work\Rewards4Platform\API\Rewards4Platform.API\bin\Debug\net8.0\Rewards4Platform.API.runtimeconfig.json
C:\Work\Rewards4Platform\API\Rewards4Platform.API\bin\Debug\net8.0\Rewards4Platform.API.dll
C:\Work\Rewards4Platform\API\Rewards4Platform.API\bin\Debug\net8.0\Rewards4Platform.API.pdb
C:\Work\Rewards4Platform\API\Rewards4Platform.API\bin\Debug\net8.0\Azure.Core.dll
C:\Work\Rewards4Platform\API\Rewards4Platform.API\bin\Debug\net8.0\Azure.Identity.dll
C:\Work\Rewards4Platform\API\Rewards4Platform.API\bin\Debug\net8.0\Dapper.dll
C:\Work\Rewards4Platform\API\Rewards4Platform.API\bin\Debug\net8.0\Microsoft.AspNetCore.Authentication.JwtBearer.dll
C:\Work\Rewards4Platform\API\Rewards4Platform.API\bin\Debug\net8.0\Microsoft.Bcl.AsyncInterfaces.dll
C:\Work\Rewards4Platform\API\Rewards4Platform.API\bin\Debug\net8.0\Microsoft.Data.SqlClient.dll
C:\Work\Rewards4Platform\API\Rewards4Platform.API\bin\Debug\net8.0\Microsoft.Identity.Client.dll
C:\Work\Rewards4Platform\API\Rewards4Platform.API\bin\Debug\net8.0\Microsoft.Identity.Client.Extensions.Msal.dll
C:\Work\Rewards4Platform\API\Rewards4Platform.API\bin\Debug\net8.0\Microsoft.IdentityModel.Abstractions.dll
C:\Work\Rewards4Platform\API\Rewards4Platform.API\bin\Debug\net8.0\Microsoft.IdentityModel.JsonWebTokens.dll
C:\Work\Rewards4Platform\API\Rewards4Platform.API\bin\Debug\net8.0\Microsoft.IdentityModel.Logging.dll
C:\Work\Rewards4Platform\API\Rewards4Platform.API\bin\Debug\net8.0\Microsoft.IdentityModel.Protocols.dll
C:\Work\Rewards4Platform\API\Rewards4Platform.API\bin\Debug\net8.0\Microsoft.IdentityModel.Protocols.OpenIdConnect.dll
C:\Work\Rewards4Platform\API\Rewards4Platform.API\bin\Debug\net8.0\Microsoft.IdentityModel.Tokens.dll
C:\Work\Rewards4Platform\API\Rewards4Platform.API\bin\Debug\net8.0\Microsoft.OpenApi.dll
C:\Work\Rewards4Platform\API\Rewards4Platform.API\bin\Debug\net8.0\Microsoft.SqlServer.Server.dll
C:\Work\Rewards4Platform\API\Rewards4Platform.API\bin\Debug\net8.0\Swashbuckle.AspNetCore.Swagger.dll
C:\Work\Rewards4Platform\API\Rewards4Platform.API\bin\Debug\net8.0\Swashbuckle.AspNetCore.SwaggerGen.dll
C:\Work\Rewards4Platform\API\Rewards4Platform.API\bin\Debug\net8.0\Swashbuckle.AspNetCore.SwaggerUI.dll
C:\Work\Rewards4Platform\API\Rewards4Platform.API\bin\Debug\net8.0\System.Configuration.ConfigurationManager.dll
C:\Work\Rewards4Platform\API\Rewards4Platform.API\bin\Debug\net8.0\System.IdentityModel.Tokens.Jwt.dll
C:\Work\Rewards4Platform\API\Rewards4Platform.API\bin\Debug\net8.0\System.Memory.Data.dll
C:\Work\Rewards4Platform\API\Rewards4Platform.API\bin\Debug\net8.0\System.Security.Cryptography.ProtectedData.dll
C:\Work\Rewards4Platform\API\Rewards4Platform.API\bin\Debug\net8.0\de\Microsoft.Data.SqlClient.resources.dll
C:\Work\Rewards4Platform\API\Rewards4Platform.API\bin\Debug\net8.0\es\Microsoft.Data.SqlClient.resources.dll
C:\Work\Rewards4Platform\API\Rewards4Platform.API\bin\Debug\net8.0\fr\Microsoft.Data.SqlClient.resources.dll
C:\Work\Rewards4Platform\API\Rewards4Platform.API\bin\Debug\net8.0\it\Microsoft.Data.SqlClient.resources.dll
C:\Work\Rewards4Platform\API\Rewards4Platform.API\bin\Debug\net8.0\ja\Microsoft.Data.SqlClient.resources.dll
C:\Work\Rewards4Platform\API\Rewards4Platform.API\bin\Debug\net8.0\ko\Microsoft.Data.SqlClient.resources.dll
C:\Work\Rewards4Platform\API\Rewards4Platform.API\bin\Debug\net8.0\pt-BR\Microsoft.Data.SqlClient.resources.dll
C:\Work\Rewards4Platform\API\Rewards4Platform.API\bin\Debug\net8.0\ru\Microsoft.Data.SqlClient.resources.dll
C:\Work\Rewards4Platform\API\Rewards4Platform.API\bin\Debug\net8.0\zh-Hans\Microsoft.Data.SqlClient.resources.dll
C:\Work\Rewards4Platform\API\Rewards4Platform.API\bin\Debug\net8.0\zh-Hant\Microsoft.Data.SqlClient.resources.dll
C:\Work\Rewards4Platform\API\Rewards4Platform.API\bin\Debug\net8.0\runtimes\unix\lib\net8.0\Microsoft.Data.SqlClient.dll
C:\Work\Rewards4Platform\API\Rewards4Platform.API\bin\Debug\net8.0\runtimes\win\lib\net8.0\Microsoft.Data.SqlClient.dll
C:\Work\Rewards4Platform\API\Rewards4Platform.API\bin\Debug\net8.0\runtimes\win-arm64\native\Microsoft.Data.SqlClient.SNI.dll
C:\Work\Rewards4Platform\API\Rewards4Platform.API\bin\Debug\net8.0\runtimes\win-x64\native\Microsoft.Data.SqlClient.SNI.dll
C:\Work\Rewards4Platform\API\Rewards4Platform.API\bin\Debug\net8.0\runtimes\win-x86\native\Microsoft.Data.SqlClient.SNI.dll
C:\Work\Rewards4Platform\API\Rewards4Platform.API\obj\Debug\net8.0\Rewards4Platform.API.csproj.AssemblyReference.cache
C:\Work\Rewards4Platform\API\Rewards4Platform.API\obj\Debug\net8.0\Rewards4Platform.API.GeneratedMSBuildEditorConfig.editorconfig
C:\Work\Rewards4Platform\API\Rewards4Platform.API\obj\Debug\net8.0\Rewards4Platform.API.AssemblyInfoInputs.cache
C:\Work\Rewards4Platform\API\Rewards4Platform.API\obj\Debug\net8.0\Rewards4Platform.API.AssemblyInfo.cs
C:\Work\Rewards4Platform\API\Rewards4Platform.API\obj\Debug\net8.0\Rewards4Platform.API.csproj.CoreCompileInputs.cache
C:\Work\Rewards4Platform\API\Rewards4Platform.API\obj\Debug\net8.0\Rewards4Platform.API.MvcApplicationPartsAssemblyInfo.cs
C:\Work\Rewards4Platform\API\Rewards4Platform.API\obj\Debug\net8.0\Rewards4Platform.API.MvcApplicationPartsAssemblyInfo.cache
C:\Work\Rewards4Platform\API\Rewards4Platform.API\obj\Debug\net8.0\Rewards4Platform.API.sourcelink.json
C:\Work\Rewards4Platform\API\Rewards4Platform.API\obj\Debug\net8.0\staticwebassets.build.json
C:\Work\Rewards4Platform\API\Rewards4Platform.API\obj\Debug\net8.0\staticwebassets.development.json
C:\Work\Rewards4Platform\API\Rewards4Platform.API\obj\Debug\net8.0\scopedcss\bundle\Rewards4Platform.API.styles.css
C:\Work\Rewards4Platform\API\Rewards4Platform.API\obj\Debug\net8.0\Rewards4.51C20300.Up2Date
C:\Work\Rewards4Platform\API\Rewards4Platform.API\obj\Debug\net8.0\Rewards4Platform.API.dll
C:\Work\Rewards4Platform\API\Rewards4Platform.API\obj\Debug\net8.0\refint\Rewards4Platform.API.dll
C:\Work\Rewards4Platform\API\Rewards4Platform.API\obj\Debug\net8.0\Rewards4Platform.API.pdb
C:\Work\Rewards4Platform\API\Rewards4Platform.API\obj\Debug\net8.0\Rewards4Platform.API.genruntimeconfig.cache
C:\Work\Rewards4Platform\API\Rewards4Platform.API\obj\Debug\net8.0\ref\Rewards4Platform.API.dll
C:\Work\Rewards4Platform\API\Rewards4Platform.API\bin\Debug\net8.0\appsettings.json
C:\Work\Rewards4Platform\API\Rewards4Platform.API\bin\Debug\net8.0\Rewards4Platform.API.staticwebassets.endpoints.json
C:\Work\Rewards4Platform\API\Rewards4Platform.API\obj\Debug\net8.0\rpswa.dswa.cache.json
C:\Work\Rewards4Platform\API\Rewards4Platform.API\obj\Debug\net8.0\rjimswa.dswa.cache.json
C:\Work\Rewards4Platform\API\Rewards4Platform.API\obj\Debug\net8.0\rjsmrazor.dswa.cache.json
C:\Work\Rewards4Platform\API\Rewards4Platform.API\obj\Debug\net8.0\rjsmcshtml.dswa.cache.json
C:\Work\Rewards4Platform\API\Rewards4Platform.API\obj\Debug\net8.0\staticwebassets.build.json.cache
C:\Work\Rewards4Platform\API\Rewards4Platform.API\obj\Debug\net8.0\staticwebassets.build.endpoints.json
C:\Work\Rewards4Platform\API\Rewards4Platform.API\obj\Debug\net8.0\staticwebassets.upToDateCheck.txt
C:\Work\Rewards4Platform\API\Rewards4Platform.API\bin\Debug\net8.0\Exceptionless.dll
C:\Work\Rewards4Platform\API\Rewards4Platform.API\bin\Debug\net8.0\Exceptionless.AspNetCore.dll
C:\Work\Rewards4Platform\API\Rewards4Platform.API\bin\Debug\net8.0\Exceptionless.Extensions.Hosting.dll
C:\Work\Rewards4Platform\API\Rewards4Platform.API\bin\Debug\net8.0\Microsoft.Extensions.DiagnosticAdapter.dll
C:\Work\Rewards4Platform\API\Rewards4Platform.API\bin\Debug\net8.0\Microsoft.Bcl.Cryptography.dll
C:\Work\Rewards4Platform\API\Rewards4Platform.API\bin\Debug\net8.0\Microsoft.Extensions.Caching.Memory.dll
C:\Work\Rewards4Platform\API\Rewards4Platform.API\bin\Debug\net8.0\Microsoft.Extensions.DependencyInjection.Abstractions.dll
C:\Work\Rewards4Platform\API\Rewards4Platform.API\bin\Debug\net8.0\Microsoft.Extensions.Logging.Abstractions.dll
C:\Work\Rewards4Platform\API\Rewards4Platform.API\bin\Debug\net8.0\Microsoft.Extensions.Options.dll
C:\Work\Rewards4Platform\API\Rewards4Platform.API\bin\Debug\net8.0\System.ClientModel.dll
C:\Work\Rewards4Platform\API\Rewards4Platform.API\bin\Debug\net8.0\System.Diagnostics.EventLog.dll
C:\Work\Rewards4Platform\API\Rewards4Platform.API\bin\Debug\net8.0\System.Security.Cryptography.Pkcs.dll
C:\Work\Rewards4Platform\API\Rewards4Platform.API\bin\Debug\net8.0\cs\Microsoft.Data.SqlClient.resources.dll
C:\Work\Rewards4Platform\API\Rewards4Platform.API\bin\Debug\net8.0\pl\Microsoft.Data.SqlClient.resources.dll
C:\Work\Rewards4Platform\API\Rewards4Platform.API\bin\Debug\net8.0\tr\Microsoft.Data.SqlClient.resources.dll
C:\Work\Rewards4Platform\API\Rewards4Platform.API\bin\Debug\net8.0\runtimes\win\lib\net8.0\System.Diagnostics.EventLog.dll
C:\Work\Rewards4Platform\API\Rewards4Platform.API\bin\Debug\net8.0\runtimes\win\lib\net8.0\System.Security.Cryptography.Pkcs.dll
