﻿namespace Rewards4Platform.API.Models
{
    public class User
    {
        public Guid Id { get; set; }
        public string FirstName { get; set; } = string.Empty;
        public string LastName { get; set; } = string.Empty;
        public string Email { get; set; } = string.Empty;
        public string Password { get; set; } = string.Empty;
        public string Salt { get; set; } = string.Empty;
        public DateTime InsertDate { get; set; }
        public DateTime? UpdateDate { get; set; }
    }
}
