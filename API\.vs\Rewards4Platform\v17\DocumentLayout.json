{"Version": 1, "WorkspaceRootPath": "C:\\Work\\Rewards4Platform\\API\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{2F8A5C3D-1E4B-4C7A-9F2E-8D3B6A1C5E7F}|..\\Rewards4Platform.Common\\Rewards4Platform.Common.csproj|c:\\work\\rewards4platform\\rewards4platform.common\\extensions\\redisextensionmethods.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{1F70BD32-F23E-4627-8FB9-C67336120F28}|Rewards4Platform.API\\Rewards4Platform.API.csproj|c:\\work\\rewards4platform\\api\\rewards4platform.api\\appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{1F70BD32-F23E-4627-8FB9-C67336120F28}|Rewards4Platform.API\\Rewards4Platform.API.csproj|solutionrelative:rewards4platform.api\\appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{1F70BD32-F23E-4627-8FB9-C67336120F28}|Rewards4Platform.API\\Rewards4Platform.API.csproj|c:\\work\\rewards4platform\\api\\rewards4platform.api\\extensions\\dependencyinjection.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{1F70BD32-F23E-4627-8FB9-C67336120F28}|Rewards4Platform.API\\Rewards4Platform.API.csproj|solutionrelative:rewards4platform.api\\extensions\\dependencyinjection.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{1F70BD32-F23E-4627-8FB9-C67336120F28}|Rewards4Platform.API\\Rewards4Platform.API.csproj|c:\\work\\rewards4platform\\api\\rewards4platform.api\\data\\sqlconnectionfactory.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{1F70BD32-F23E-4627-8FB9-C67336120F28}|Rewards4Platform.API\\Rewards4Platform.API.csproj|solutionrelative:rewards4platform.api\\data\\sqlconnectionfactory.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{1F70BD32-F23E-4627-8FB9-C67336120F28}|Rewards4Platform.API\\Rewards4Platform.API.csproj|c:\\work\\rewards4platform\\api\\rewards4platform.api\\models\\user.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{1F70BD32-F23E-4627-8FB9-C67336120F28}|Rewards4Platform.API\\Rewards4Platform.API.csproj|solutionrelative:rewards4platform.api\\models\\user.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{1F70BD32-F23E-4627-8FB9-C67336120F28}|Rewards4Platform.API\\Rewards4Platform.API.csproj|c:\\work\\rewards4platform\\api\\rewards4platform.api\\program.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{1F70BD32-F23E-4627-8FB9-C67336120F28}|Rewards4Platform.API\\Rewards4Platform.API.csproj|solutionrelative:rewards4platform.api\\program.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{1F70BD32-F23E-4627-8FB9-C67336120F28}|Rewards4Platform.API\\Rewards4Platform.API.csproj|c:\\work\\rewards4platform\\api\\rewards4platform.api\\controllers\\usercontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{1F70BD32-F23E-4627-8FB9-C67336120F28}|Rewards4Platform.API\\Rewards4Platform.API.csproj|solutionrelative:rewards4platform.api\\controllers\\usercontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{1F70BD32-F23E-4627-8FB9-C67336120F28}|Rewards4Platform.API\\Rewards4Platform.API.csproj|c:\\work\\rewards4platform\\api\\rewards4platform.api\\properties\\launchsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{1F70BD32-F23E-4627-8FB9-C67336120F28}|Rewards4Platform.API\\Rewards4Platform.API.csproj|solutionrelative:rewards4platform.api\\properties\\launchsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{1F70BD32-F23E-4627-8FB9-C67336120F28}|Rewards4Platform.API\\Rewards4Platform.API.csproj|c:\\work\\rewards4platform\\api\\rewards4platform.api\\requests\\loginrequest.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{1F70BD32-F23E-4627-8FB9-C67336120F28}|Rewards4Platform.API\\Rewards4Platform.API.csproj|solutionrelative:rewards4platform.api\\requests\\loginrequest.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{1F70BD32-F23E-4627-8FB9-C67336120F28}|Rewards4Platform.API\\Rewards4Platform.API.csproj|c:\\work\\rewards4platform\\api\\rewards4platform.api\\services\\jwtprovider.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{1F70BD32-F23E-4627-8FB9-C67336120F28}|Rewards4Platform.API\\Rewards4Platform.API.csproj|solutionrelative:rewards4platform.api\\services\\jwtprovider.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{1F70BD32-F23E-4627-8FB9-C67336120F28}|Rewards4Platform.API\\Rewards4Platform.API.csproj|c:\\work\\rewards4platform\\api\\rewards4platform.api\\services\\passwordhashingservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{1F70BD32-F23E-4627-8FB9-C67336120F28}|Rewards4Platform.API\\Rewards4Platform.API.csproj|solutionrelative:rewards4platform.api\\services\\passwordhashingservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{1F70BD32-F23E-4627-8FB9-C67336120F28}|Rewards4Platform.API\\Rewards4Platform.API.csproj|c:\\work\\rewards4platform\\api\\rewards4platform.api\\requests\\registrationrequest.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{1F70BD32-F23E-4627-8FB9-C67336120F28}|Rewards4Platform.API\\Rewards4Platform.API.csproj|solutionrelative:rewards4platform.api\\requests\\registrationrequest.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{2F8A5C3D-1E4B-4C7A-9F2E-8D3B6A1C5E7F}|..\\Rewards4Platform.Common\\Rewards4Platform.Common.csproj|c:\\work\\rewards4platform\\rewards4platform.common\\helpers\\redishelper.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{2F8A5C3D-1E4B-4C7A-9F2E-8D3B6A1C5E7F}|..\\Rewards4Platform.Common\\Rewards4Platform.Common.csproj|c:\\work\\rewards4platform\\rewards4platform.common\\constants\\cache\\cachekey.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{2F8A5C3D-1E4B-4C7A-9F2E-8D3B6A1C5E7F}|..\\Rewards4Platform.Common\\Rewards4Platform.Common.csproj|c:\\work\\rewards4platform\\rewards4platform.common\\constants\\cache\\cacheduration.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{2F8A5C3D-1E4B-4C7A-9F2E-8D3B6A1C5E7F}|..\\Rewards4Platform.Common\\Rewards4Platform.Common.csproj|c:\\work\\rewards4platform\\rewards4platform.common\\configurations\\cache\\redisconfigurationoptionssetup.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{2F8A5C3D-1E4B-4C7A-9F2E-8D3B6A1C5E7F}|..\\Rewards4Platform.Common\\Rewards4Platform.Common.csproj|c:\\work\\rewards4platform\\rewards4platform.common\\configurations\\cache\\redisconfigurationoptions.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 1, "Children": [{"$type": "Bookmark", "Name": "ST:133:0:{116d2292-e37d-41cd-a077-ebacac4c8cc4}"}, {"$type": "Document", "DocumentIndex": 0, "Title": "RedisExtensionMethods.cs", "DocumentMoniker": "C:\\Work\\Rewards4Platform\\Rewards4Platform.Common\\Extensions\\RedisExtensionMethods.cs", "RelativeDocumentMoniker": "..\\Rewards4Platform.Common\\Extensions\\RedisExtensionMethods.cs", "ToolTip": "C:\\Work\\Rewards4Platform\\Rewards4Platform.Common\\Extensions\\RedisExtensionMethods.cs", "RelativeToolTip": "..\\Rewards4Platform.Common\\Extensions\\RedisExtensionMethods.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABEAAABIAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-05T19:58:29.541Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 12, "Title": "RedisHelper.cs", "DocumentMoniker": "C:\\Work\\Rewards4Platform\\Rewards4Platform.Common\\Helpers\\RedisHelper.cs", "RelativeDocumentMoniker": "..\\Rewards4Platform.Common\\Helpers\\RedisHelper.cs", "ToolTip": "C:\\Work\\Rewards4Platform\\Rewards4Platform.Common\\Helpers\\RedisHelper.cs", "RelativeToolTip": "..\\Rewards4Platform.Common\\Helpers\\RedisHelper.cs", "ViewState": "AgIAAAMAAAAAAAAAAAAAAAgAAABHAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-05T19:57:20.342Z"}, {"$type": "Document", "DocumentIndex": 13, "Title": "CacheKey.cs", "DocumentMoniker": "C:\\Work\\Rewards4Platform\\Rewards4Platform.Common\\Constants\\Cache\\CacheKey.cs", "RelativeDocumentMoniker": "..\\Rewards4Platform.Common\\Constants\\Cache\\CacheKey.cs", "ToolTip": "C:\\Work\\Rewards4Platform\\Rewards4Platform.Common\\Constants\\Cache\\CacheKey.cs", "RelativeToolTip": "..\\Rewards4Platform.Common\\Constants\\Cache\\CacheKey.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAMAAAAFAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-05T19:55:37.135Z"}, {"$type": "Document", "DocumentIndex": 14, "Title": "CacheDuration.cs", "DocumentMoniker": "C:\\Work\\Rewards4Platform\\Rewards4Platform.Common\\Constants\\Cache\\CacheDuration.cs", "RelativeDocumentMoniker": "..\\Rewards4Platform.Common\\Constants\\Cache\\CacheDuration.cs", "ToolTip": "C:\\Work\\Rewards4Platform\\Rewards4Platform.Common\\Constants\\Cache\\CacheDuration.cs", "RelativeToolTip": "..\\Rewards4Platform.Common\\Constants\\Cache\\CacheDuration.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAMAAAAFAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-05T19:54:57.277Z"}, {"$type": "Document", "DocumentIndex": 15, "Title": "RedisConfigurationOptionsSetup.cs", "DocumentMoniker": "C:\\Work\\Rewards4Platform\\Rewards4Platform.Common\\Configurations\\Cache\\RedisConfigurationOptionsSetup.cs", "RelativeDocumentMoniker": "..\\Rewards4Platform.Common\\Configurations\\Cache\\RedisConfigurationOptionsSetup.cs", "ToolTip": "C:\\Work\\Rewards4Platform\\Rewards4Platform.Common\\Configurations\\Cache\\RedisConfigurationOptionsSetup.cs", "RelativeToolTip": "..\\Rewards4Platform.Common\\Configurations\\Cache\\RedisConfigurationOptionsSetup.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAA4AAABAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-05T19:50:55.562Z"}, {"$type": "Document", "DocumentIndex": 16, "Title": "RedisConfigurationOptions.cs", "DocumentMoniker": "C:\\Work\\Rewards4Platform\\Rewards4Platform.Common\\Configurations\\Cache\\RedisConfigurationOptions.cs", "RelativeDocumentMoniker": "..\\Rewards4Platform.Common\\Configurations\\Cache\\RedisConfigurationOptions.cs", "ToolTip": "C:\\Work\\Rewards4Platform\\Rewards4Platform.Common\\Configurations\\Cache\\RedisConfigurationOptions.cs", "RelativeToolTip": "..\\Rewards4Platform.Common\\Configurations\\Cache\\RedisConfigurationOptions.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAQAAAA+AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-05T19:50:10.154Z"}, {"$type": "Document", "DocumentIndex": 2, "Title": "DependencyInjection.cs", "DocumentMoniker": "C:\\Work\\Rewards4Platform\\API\\Rewards4Platform.API\\Extensions\\DependencyInjection.cs", "RelativeDocumentMoniker": "Rewards4Platform.API\\Extensions\\DependencyInjection.cs", "ToolTip": "C:\\Work\\Rewards4Platform\\API\\Rewards4Platform.API\\Extensions\\DependencyInjection.cs", "RelativeToolTip": "Rewards4Platform.API\\Extensions\\DependencyInjection.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAgAAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-05T13:33:27.553Z"}, {"$type": "Document", "DocumentIndex": 3, "Title": "SqlConnectionFactory.cs", "DocumentMoniker": "C:\\Work\\Rewards4Platform\\API\\Rewards4Platform.API\\Data\\SqlConnectionFactory.cs", "RelativeDocumentMoniker": "Rewards4Platform.API\\Data\\SqlConnectionFactory.cs", "ToolTip": "C:\\Work\\Rewards4Platform\\API\\Rewards4Platform.API\\Data\\SqlConnectionFactory.cs", "RelativeToolTip": "Rewards4Platform.API\\Data\\SqlConnectionFactory.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABcAAAAJAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-05T13:25:58.585Z"}, {"$type": "Document", "DocumentIndex": 5, "Title": "Program.cs", "DocumentMoniker": "C:\\Work\\Rewards4Platform\\API\\Rewards4Platform.API\\Program.cs", "RelativeDocumentMoniker": "Rewards4Platform.API\\Program.cs", "ToolTip": "C:\\Work\\Rewards4Platform\\API\\Rewards4Platform.API\\Program.cs", "RelativeToolTip": "Rewards4Platform.API\\Program.cs", "ViewState": "AgIAAAkAAAAAAAAAAIBJwBcAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-31T09:30:38.475Z"}, {"$type": "Document", "DocumentIndex": 7, "Title": "launchSettings.json", "DocumentMoniker": "C:\\Work\\Rewards4Platform\\API\\Rewards4Platform.API\\Properties\\launchSettings.json", "RelativeDocumentMoniker": "Rewards4Platform.API\\Properties\\launchSettings.json", "ToolTip": "C:\\Work\\Rewards4Platform\\API\\Rewards4Platform.API\\Properties\\launchSettings.json", "RelativeToolTip": "Rewards4Platform.API\\Properties\\launchSettings.json", "ViewState": "AgIAAAAAAAAAAAAAAAAAABoAAAAvAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-07-28T10:25:09.702Z"}, {"$type": "Document", "DocumentIndex": 9, "Title": "JwtProvider.cs", "DocumentMoniker": "C:\\Work\\Rewards4Platform\\API\\Rewards4Platform.API\\Services\\JwtProvider.cs", "RelativeDocumentMoniker": "Rewards4Platform.API\\Services\\JwtProvider.cs", "ToolTip": "C:\\Work\\Rewards4Platform\\API\\Rewards4Platform.API\\Services\\JwtProvider.cs", "RelativeToolTip": "Rewards4Platform.API\\Services\\JwtProvider.cs", "ViewState": "AgIAAAsAAAAAAAAAAAAcwBYAAAAiAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-09-11T12:21:29.141Z"}, {"$type": "Document", "DocumentIndex": 10, "Title": "PasswordHashingService.cs", "DocumentMoniker": "C:\\Work\\Rewards4Platform\\API\\Rewards4Platform.API\\Services\\PasswordHashingService.cs", "RelativeDocumentMoniker": "Rewards4Platform.API\\Services\\PasswordHashingService.cs", "ToolTip": "C:\\Work\\Rewards4Platform\\API\\Rewards4Platform.API\\Services\\PasswordHashingService.cs", "RelativeToolTip": "Rewards4Platform.API\\Services\\PasswordHashingService.cs", "ViewState": "AgIAAAIAAAAAAAAAAAAuwCUAAAAJAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-09-11T11:28:28.411Z"}, {"$type": "Document", "DocumentIndex": 8, "Title": "LoginRequest.cs", "DocumentMoniker": "C:\\Work\\Rewards4Platform\\API\\Rewards4Platform.API\\Requests\\LoginRequest.cs", "RelativeDocumentMoniker": "Rewards4Platform.API\\Requests\\LoginRequest.cs", "ToolTip": "C:\\Work\\Rewards4Platform\\API\\Rewards4Platform.API\\Requests\\LoginRequest.cs", "RelativeToolTip": "Rewards4Platform.API\\Requests\\LoginRequest.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAIAAAAdAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-09-11T11:26:54.699Z"}, {"$type": "Document", "DocumentIndex": 11, "Title": "RegistrationRequest.cs", "DocumentMoniker": "C:\\Work\\Rewards4Platform\\API\\Rewards4Platform.API\\Requests\\RegistrationRequest.cs", "RelativeDocumentMoniker": "Rewards4Platform.API\\Requests\\RegistrationRequest.cs", "ToolTip": "C:\\Work\\Rewards4Platform\\API\\Rewards4Platform.API\\Requests\\RegistrationRequest.cs", "RelativeToolTip": "Rewards4Platform.API\\Requests\\RegistrationRequest.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAcAAAA8AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-09-11T11:24:47.193Z"}, {"$type": "Document", "DocumentIndex": 4, "Title": "User.cs", "DocumentMoniker": "C:\\Work\\Rewards4Platform\\API\\Rewards4Platform.API\\Models\\User.cs", "RelativeDocumentMoniker": "Rewards4Platform.API\\Models\\User.cs", "ToolTip": "C:\\Work\\Rewards4Platform\\API\\Rewards4Platform.API\\Models\\User.cs", "RelativeToolTip": "Rewards4Platform.API\\Models\\User.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAIAAAATAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-09-11T10:09:28.126Z"}, {"$type": "Document", "DocumentIndex": 1, "Title": "appsettings.json", "DocumentMoniker": "C:\\Work\\Rewards4Platform\\API\\Rewards4Platform.API\\appsettings.json", "RelativeDocumentMoniker": "Rewards4Platform.API\\appsettings.json", "ToolTip": "C:\\Work\\Rewards4Platform\\API\\Rewards4Platform.API\\appsettings.json", "RelativeToolTip": "Rewards4Platform.API\\appsettings.json", "ViewState": "AgIAAAAAAAAAAAAAAAAAABgAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2024-06-27T11:41:31.545Z"}, {"$type": "Document", "DocumentIndex": 6, "Title": "UserController.cs", "DocumentMoniker": "C:\\Work\\Rewards4Platform\\API\\Rewards4Platform.API\\Controllers\\UserController.cs", "RelativeDocumentMoniker": "Rewards4Platform.API\\Controllers\\UserController.cs", "ToolTip": "C:\\Work\\Rewards4Platform\\API\\Rewards4Platform.API\\Controllers\\UserController.cs", "RelativeToolTip": "Rewards4Platform.API\\Controllers\\UserController.cs", "ViewState": "AgIAABYAAAAAAAAAAAAYwC4AAAAIAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-06-27T11:21:13.386Z"}]}]}]}