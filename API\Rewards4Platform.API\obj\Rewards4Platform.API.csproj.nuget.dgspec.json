{"format": 1, "restore": {"C:\\Work\\Rewards4Platform\\API\\Rewards4Platform.API\\Rewards4Platform.API.csproj": {}}, "projects": {"C:\\Work\\Rewards4Platform\\API\\Rewards4Platform.API\\Rewards4Platform.API.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Work\\Rewards4Platform\\API\\Rewards4Platform.API\\Rewards4Platform.API.csproj", "projectName": "Rewards4Platform.API", "projectPath": "C:\\Work\\Rewards4Platform\\API\\Rewards4Platform.API\\Rewards4Platform.API.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Work\\Rewards4Platform\\API\\Rewards4Platform.API\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}, "https://nuget.hangfire.io/nuget/hangfire-pro/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Dapper": {"target": "Package", "version": "[2.1.66, )"}, "Exceptionless.AspNetCore": {"target": "Package", "version": "[6.1.0, )"}, "Microsoft.AspNetCore.Authentication.JwtBearer": {"target": "Package", "version": "[8.0.18, )"}, "Microsoft.Data.SqlClient": {"target": "Package", "version": "[6.1.0, )"}, "Swashbuckle.AspNetCore": {"target": "Package", "version": "[8.1.4, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.302/PortableRuntimeIdentifierGraph.json"}}}}}