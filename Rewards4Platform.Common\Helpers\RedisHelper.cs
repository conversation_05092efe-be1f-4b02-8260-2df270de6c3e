﻿using Microsoft.Extensions.Options;
using Rewards4Platform.Common.Configurations.Cache;
using StackExchange.Redis;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Rewards4Platform.Common.Helpers
{
    public class RedisHelper
    {
        private readonly IConnectionMultiplexer _connectionMultiplexer;
        private readonly RedisConfigurationOptions _redisConfiguration;

        public RedisHelper(IOptions<RedisConfigurationOptions> redisConfiguration, IConnectionMultiplexer connectionMultiplexer)
        {
            _redisConfiguration = redisConfiguration.Value;
            _connectionMultiplexer = connectionMultiplexer;
        }

        public void ClearCmsRedisCache()
        {
            var cmsCachingPattern = "*" + _redisConfiguration.Cms!.KeyPrefix + "*";
            var server = _connectionMultiplexer.GetServer($"{_redisConfiguration.ServerName}:{_redisConfiguration.Port}");

            var cmsCachingDatabase = _connectionMultiplexer.GetDatabase(_redisConfiguration.Cms.Database);
            foreach (var key in server.Keys(pattern: cmsCachingPattern, pageSize: 200000))
            {
                cmsCachingDatabase.KeyDeleteAsync(key);
            }
        }

        public void ClearWebsiteRedisCache()
        {
            var websiteCahingPattern = _redisConfiguration.Website!.KeyPrefix + "competition:api:" + "*";

            var server = _connectionMultiplexer.GetServer($"{_redisConfiguration.ServerName}:{_redisConfiguration.Port}");
            var websiteCachingDatabase = _connectionMultiplexer.GetDatabase(_redisConfiguration.Website.Database);

            foreach (var key in server.Keys(pattern: websiteCahingPattern, pageSize: 200000))
            {
                websiteCachingDatabase.KeyDelete(key);
            }
        }
    }
}
