﻿using System.Security.Cryptography;

namespace Rewards4Platform.API.Services
{
    public interface IPasswordHashingService
    {
        Task<string> HashPassword(string password);

        Task<bool> VerifyPassword(string hashPassword, string salt, string inputPassword);
    }

    public class PasswordHashingService : IPasswordHashingService
    {
        private const int saltSize = 128 / 8;
        private const int keySize = 256 / 8;
        private const int iterations = 10000;
        private static readonly HashAlgorithmName hashAlgorithm = HashAlgorithmName.SHA256;
        private const char delimeter = ':'; 

        public async Task<string> HashPassword(string password)
        {
            var salt = RandomNumberGenerator.GetBytes(saltSize);
            var hash = Rfc2898DeriveBytes.Pbkdf2(password, salt, iterations, hashAlgorithm, keySize);

            return await Task.FromResult(string.Join(delimeter, Convert.ToBase64String(hash), Convert.ToBase64String(salt)));
        }

        public async Task<bool> VerifyPassword(string hashPassword, string hashsalt, string inputPassword)
        {
            var hash = Convert.FromBase64String(hashPassword);
            var salt = Convert.FromBase64String(hashsalt);            

            var inputPasswordHash = Rfc2898DeriveBytes.Pbkdf2(inputPassword, salt, iterations, hashAlgorithm, keySize);

            var isValid = CryptographicOperations.FixedTimeEquals(hash, inputPasswordHash);

            return await Task.FromResult(isValid);
        }
    }
}
