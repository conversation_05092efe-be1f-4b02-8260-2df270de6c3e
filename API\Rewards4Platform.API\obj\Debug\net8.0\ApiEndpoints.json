[{"ContainingType": "Rewards4Platform.API.Controllers.UserController", "Method": "<PERSON><PERSON>", "RelativePath": "api/user/login", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Rewards4Platform.API.Requests.LoginRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Rewards4Platform.API.Controllers.UserController", "Method": "Logout", "RelativePath": "api/user/logout", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "Rewards4Platform.API.Controllers.UserController", "Method": "Register", "RelativePath": "api/user/register", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Rewards4Platform.API.Requests.RegistrationRequest", "IsRequired": true}], "ReturnTypes": []}]