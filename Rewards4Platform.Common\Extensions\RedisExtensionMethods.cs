﻿using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Options;
using Rewards4Platform.Common.Configurations.Cache;
using StackExchange.Redis;
using StackExchange.Redis.Extensions.Core;
using StackExchange.Redis.Extensions.Core.Abstractions;
using StackExchange.Redis.Extensions.Core.Configuration;
using StackExchange.Redis.Extensions.Core.Implementations;

namespace Rewards4Platform.Common.Extensions
{
    public static class RedisExtensionMethods
    {
        public static IServiceCollection AddStackExchangeRedisExtensions<T>(this IServiceCollection services, IConfiguration configuration)
           where T : class, ISerializer, new()
        {
            services.ConfigureOptions<RedisConfigurationOptionsSetup>();

            services.AddSingleton<IRedisClientFactory, RedisClientFactory>();

            services.AddSingleton<ISerializer, T>();

            services.AddSingleton<IConnectionMultiplexer>(provider =>
            {
                var redisOptions = provider.GetRequiredService<IOptions<RedisConfigurationOptions>>().Value;

                var configurationOptions = new ConfigurationOptions
                {
                    EndPoints = { { redisOptions.ServerName, redisOptions.Port } },
                    Password = redisOptions.Password,
                    Ssl = redisOptions.UseSsl,
                    ConnectTimeout = 10000,
                    SyncTimeout = 10000,
                    AbortOnConnectFail = false,
                    DefaultDatabase = 3
                };

                return ConnectionMultiplexer.Connect(configurationOptions);
            });

            services.AddSingleton(provider =>
            {
                var redisOptions = provider.GetRequiredService<IOptions<RedisConfigurationOptions>>().Value;

                var redisConfiguration = new RedisConfiguration()
                {
                    AbortOnConnectFail = false,
                    KeyPrefix = redisOptions.Website!.KeyPrefix,
                    Hosts =
                    [
                        new RedisHost
                        {
                            Host = redisOptions.ServerName,
                            Port = redisOptions.Port
                        }
                    ],
                    Password = redisOptions.Password,
                    AllowAdmin = true,
                    ConnectTimeout = 10000,
                    Database = redisOptions.Website.Database,
                    Ssl = redisOptions.UseSsl,

                    ServerEnumerationStrategy = new ServerEnumerationStrategy()
                    {
                        Mode = ServerEnumerationStrategy.ModeOptions.All,
                        TargetRole = ServerEnumerationStrategy.TargetRoleOptions.Any,
                        UnreachableServerAction = ServerEnumerationStrategy.UnreachableServerActionOptions.Throw
                    },
                };

                return redisConfiguration;
            });

            services.AddSingleton(sp =>
                                  sp.GetRequiredService<IRedisClientFactory>()
                                    .GetDefaultRedisClient()
                                    .GetDefaultDatabase());

            return services;
        }
    }
}
