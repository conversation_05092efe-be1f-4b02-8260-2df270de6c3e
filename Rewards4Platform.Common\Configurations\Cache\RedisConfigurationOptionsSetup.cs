﻿using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Options;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Rewards4Platform.Common.Configurations.Cache
{
    public class RedisConfigurationOptionsSetup : IConfigureOptions<RedisConfigurationOptions>
    {
        private readonly IConfiguration _configuration;

        public RedisConfigurationOptionsSetup(IConfiguration configuration)
        {
            _configuration = configuration;
        }

        public void Configure(RedisConfigurationOptions options)
        {
            var redisSection = _configuration.GetSection("Redis");

            options.ServerName = redisSection["ServerName"]!;
            options.Port = int.Parse(redisSection["Port"]!);
            options.Password = redisSection["Password"]!;
            options.UseSsl = bool.Parse(redisSection["UseSsl"]!);

            options.Website = new WebsiteConfig
            {
                KeyPrefix = _configuration["Redis:Website:KeyPrefix"]!,
                Database = int.Parse(_configuration["Redis:Website:Database"]! ?? "3")
            };

            options.Cms = new CmsConfig
            {
                KeyPrefix = _configuration["Redis:Cms:KeyPrefix"]!,
                Database = int.Parse(_configuration["Redis:Cms:Database"]! ?? "3")
            };
        }
    }
}
