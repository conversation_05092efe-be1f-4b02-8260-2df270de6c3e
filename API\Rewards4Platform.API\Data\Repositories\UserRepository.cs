﻿using Dapper;
using Rewards4Platform.API.Models;

namespace Rewards4Platform.API.Data.Repositories
{
    public interface IUserRepository
    {
        Task<bool> Create<PERSON>ser(User user);
        Task<User?> GetUserByEmail(string email);
    }

    public class UserRepository : IUserRepository
    {
        private readonly ISqlConnectionFactory _sqlConnectionFactory;

        public UserRepository(ISqlConnectionFactory sqlConnectionFactory)
        {
            _sqlConnectionFactory = sqlConnectionFactory;
        }

        public async Task<bool> CreateUser(User user)
        {
            await using var sqlConnection = _sqlConnectionFactory.CreateConnection();

            var sql = """
                INSERT INTO Users (Id, FirstName, LastName, Email, Password, Salt, InsertDate, UpdateDate)
                VALUES (@Id, @FirstName, @LastName, @Email, @Password, @Salt, @InsertDate, @UpdateDate)
                """;

            var rowsAffected = await sqlConnection.ExecuteAsync(sql, user);

            return rowsAffected > 0;
        }

        public async Task<User?> GetUserByEmail(string email)
        {
            await using var sqlConnection = _sqlConnectionFactory.CreateConnection();

            var query = "SELECT * FROM Users WHERE Email = @Email";

            var user = await sqlConnection.QuerySingleOrDefaultAsync<User?>(query, new { email });

            return user;                      
        }
    }
}
