import api from "./axios";
import SignupRequest from "../requests/SingupRequest";
import SigninRequest from "../requests/SigninRequest";

class AuthenticationService {
  async signup(signupRequest: SignupRequest) {
    try {
      const response = await api.post("/user/register", signupRequest);
      return response.data;
    } catch (error) {
      if (error instanceof Error) {
        throw new Error("There was an error signing up: " + error.message);
      }
    }
  }

  async signin(signinRequest: SigninRequest) {
    try {
      const response = await api.post("/user/login", signinRequest);
      return response.data;
    } catch (error) {
      if (error instanceof Error) {
        throw new Error("There was an error signing in: " + error.message);
      }
    }
  }

  async signout() {
    try {
      await api.post("/user/logout");      
    } catch (error) {
      if (error instanceof Error) {
        throw new Error("There was an error signing up: " + error.message);
      }
    }
  }
}

export default new AuthenticationService();
