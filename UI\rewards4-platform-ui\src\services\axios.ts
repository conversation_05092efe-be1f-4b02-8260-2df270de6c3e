import axios from "axios";
import { useRouter } from "vue-router";

let baseUrl: string;

switch (true) {
  case window.location.hostname.indexOf("localhost") >= 0:
    baseUrl = "https://localhost:7150/api";
    break;
  case window.location.hostname.indexOf("test") >= 0 || window.location.hostname.indexOf("testing") >= 0:
    baseUrl = "http://neu-r4g-app-rewards4platform-api-testing.azurewebsites.net/api";
    break;
  case window.location.hostname.indexOf("staging") >= 0:
    baseUrl = "https://neu-r4g-app-rewards4platform-api-staging.azurewebsites.net/api";
    break;
  default:
    baseUrl = "https://neu-r4g-app-rewards4platform-api/api";
}

const api = axios.create({
  baseURL: baseUrl,
  withCredentials: true,
});

api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      window.location.href = '/signin';
    }
    return Promise.reject(error);
  }
);

export default api;
