import { defineStore } from 'pinia';
import { ref } from 'vue';
import AuthenticationService from '../services/authentication-service';
import SignupRequest from '../requests/SingupRequest';
import SigninRequest from '../requests/SigninRequest';

export const useAuthStore = defineStore('auth', () => {
  const isAuthenticated = ref<boolean>(false)
  const user = ref<{
    userId?: string,
    firstName?: string,
    lastName?: string
  }>({})

  const signup = async (signupRequest: SignupRequest) => {
    try {
      const response = await AuthenticationService.signup(signupRequest)
      if (response?.isSuccess === true) {        
        isAuthenticated.value = true             
      }      
      return response
    } catch (error) {
      throw error;
    }
  }

  const signin = async (signinRequest: SigninRequest) => {
    try {
      const response = await AuthenticationService.signin(signinRequest)     
      
      if (response?.isSuccess === true) {        
        isAuthenticated.value = true
        user.value = {
          userId: response.userId,
          firstName: response.firstName,
          lastName: response.lastName
        }        
      }      
      return response
    } catch (error) {
      throw error
    }
  }

  const signout = async () => {
    try {
      await AuthenticationService.signout()
      isAuthenticated.value = false
      user.value = {}
    } catch (error) {
      isAuthenticated.value = false
      user.value = {}
    }
  }

  return {
    isAuthenticated,
    user,
    signup,
    signin,
    signout
  }
})









