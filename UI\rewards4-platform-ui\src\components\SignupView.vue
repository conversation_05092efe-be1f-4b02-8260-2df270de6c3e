<template>
  <nav>
    <router-link to="/">Sign Up</router-link> |
    <router-link to="/signin">Sign In</router-link>
  </nav>

  <div class="signup">
    <form @submit.prevent="submitForm">
      <h1>Signup</h1>
      <label>First Name</label>
      <input type="text" placeholder="Enter your first name" required v-model="signupRequest.firstname">
      <label>Last Name</label>
      <input type="text" placeholder="Enter your last name" required v-model="signupRequest.lastname">
      <label>Email</label>
      <input type="email" placeholder="Enter your email name" required v-model="signupRequest.email">
      <label>Password</label>
      <input type="password" placeholder="Enter your password" required v-model="signupRequest.password">
      <br/>
      <button type="submit">Submit</button>
  </form>

  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '../stores/auth'
import SignupRequest from '../requests/SingupRequest'

const router = useRouter()
const authStore = useAuthStore()

const signupRequest = ref<SignupRequest>({
  firstname: '',
  lastname: '',
  email: '',
  password: ''
})

const submitForm = async () => {
  try {
    const response = await authStore.signup(signupRequest.value)
    if (response?.isSuccess && authStore.isAuthenticated) {
      router.push('/home')
    }
  } catch (error) {
    console.error(error)
  }
}
</script>

<style scoped>
form {
    max-width: 420px;
    margin: 30px auto;
    background: crimson;
    text-align: center;
    padding: 40px;
    border-radius: 10px;
  }
  label {
    color: #aaa;
    display: flex;
    margin: 25px 0 15px;
    font-size: 0.7em;
    text-transform: uppercase;
    letter-spacing: 1px;
    font-weight: bold;
  }
  input {
    display: block;
    padding: 10px 6px;
    width: 100%;
    box-sizing: border-box;
    border: none;
    border-bottom: 1px solid #ddd;
    color: #555;
    border-radius: 5px;
  }
  button {
    display: block;
    padding: 10px 6px;
    width: 100%;
    box-sizing: border-box;
    border: none;
    border-bottom: 1px solid #ddd;
    color: #555;
    border-radius: 5px;
    font-weight: bold;
  }
</style>
