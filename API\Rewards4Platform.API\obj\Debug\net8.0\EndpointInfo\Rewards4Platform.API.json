{"openapi": "3.0.4", "info": {"title": "Rewards4Platform.API", "version": "1.0"}, "paths": {"/api/user/register": {"post": {"tags": ["User"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RegistrationRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RegistrationRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/RegistrationRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/user/login": {"post": {"tags": ["User"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/LoginRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/LoginRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/LoginRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/user/logout": {"post": {"tags": ["User"], "responses": {"200": {"description": "OK"}}}}}, "components": {"schemas": {"LoginRequest": {"type": "object", "properties": {"email": {"type": "string", "nullable": true}, "password": {"type": "string", "nullable": true}}, "additionalProperties": false}, "RegistrationRequest": {"type": "object", "properties": {"firstName": {"type": "string", "nullable": true}, "lastName": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}, "password": {"type": "string", "nullable": true}}, "additionalProperties": false}}}}