﻿using Microsoft.IdentityModel.Tokens;
using Rewards4Platform.API.Models;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Text;

namespace Rewards4Platform.API.Services
{
    public interface IJwtProvider
    {
        Task<string> GenerateToken(User user);
    }

    public class JwtProvider : IJwtProvider   
    {
        private readonly IConfiguration _configuration;

        public JwtProvider(IConfiguration configuration)
        {
            _configuration = configuration;
        }

        public async Task<string> GenerateToken(User user)
        {
            var claims = new Claim[] 
            {
                new(JwtRegisteredClaimNames.Sub, user.Id.ToString()),
                new(JwtRegisteredClaimNames.Email, user.Email)
            };

            var signingCredentials = new SigningCredentials(
                    new SymmetricSecurityKey(Encoding.UTF8.GetBytes(_configuration["Jwt:SecretKey"]!)),
                    SecurityAlgorithms.HmacSha256);

            var token = new JwtSecurityToken(
                    _configuration["Jwt:Issuer"],
                    _configuration["Jwt:Audience"],
                    claims,
                    null,
                    DateTime.Now.AddHours(6),
                    signingCredentials);

            var tokenValue = new JwtSecurityTokenHandler().WriteToken(token);

            return await Task.FromResult(tokenValue);
        }
    }
}
